#!/usr/bin/env python3
"""
Quick test script to verify the server starts correctly
"""

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_dependencies():
    """Test that our optimized dependencies work correctly"""
    try:
        print("🧪 Testing optimized dependencies...")
        
        # Test the require function
        from app.core.security import require
        
        # Test basic require function
        basic_dep = require()
        print("✅ Basic require() function created successfully")
        
        # Test with LLM
        llm_dep = require(llm=True)
        print("✅ require(llm=True) function created successfully")
        
        # Test with Qdrant
        qdrant_dep = require(qdrant=True)
        print("✅ require(qdrant=True) function created successfully")
        
        # Test with both
        both_dep = require(llm=True, qdrant=True)
        print("✅ require(llm=True, qdrant=True) function created successfully")
        
        # Test with roles
        roles_dep = require(roles=["admin", "lawyer"])
        print("✅ require(roles=['admin', 'lawyer']) function created successfully")
        
        print("🎉 All dependency functions created successfully!")
        
    except Exception as e:
        print(f"❌ Error testing dependencies: {e}")
        return False
    
    return True

async def test_performance_optimizations():
    """Test performance optimizations"""
    try:
        print("🚀 Testing performance optimizations...")
        
        # Test connection pool
        from app.core.performance import ConnectionPool
        print("✅ ConnectionPool imported successfully")
        
        # Test cache manager
        from app.core.performance import CacheManager
        print("✅ CacheManager imported successfully")
        
        # Test warm up function
        from app.core.performance import warm_up_connections
        print("✅ warm_up_connections imported successfully")
        
        print("🎉 All performance optimizations loaded successfully!")
        
    except Exception as e:
        print(f"❌ Error testing performance optimizations: {e}")
        return False
    
    return True

async def test_app_creation():
    """Test that the FastAPI app can be created"""
    try:
        print("🏗️  Testing FastAPI app creation...")
        
        # Import the app
        from app import app
        print("✅ FastAPI app imported successfully")
        
        # Check that routes are registered
        routes = [route.path for route in app.routes]
        print(f"✅ Found {len(routes)} routes registered")
        
        # Check for key routes
        key_routes = ["/", "/health", "/hello"]
        for route in key_routes:
            if route in routes:
                print(f"✅ Route {route} found")
            else:
                print(f"⚠️  Route {route} not found")
        
        print("🎉 FastAPI app created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating FastAPI app: {e}")
        return False
    
    return True

async def main():
    """Run all tests"""
    print("🧪 Starting Legal Backend API Tests")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Performance Optimizations", test_performance_optimizations),
        ("FastAPI App Creation", test_app_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The server should start correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
