#!/usr/bin/env python3
"""
Check environment variables for API keys
"""

import os
from dotenv import load_dotenv

load_dotenv()

print("🔍 Checking API Keys Environment Variables:")
print("=" * 50)

# Check OpenAI
openai_key = os.getenv("OPENAI_API_KEY")
if openai_key:
    print(f"✅ OPENAI_API_KEY: Found (length: {len(openai_key)})")
else:
    print("❌ OPENAI_API_KEY: Not found")

# Check Gemini/Google
gemini_key = os.getenv("GEMINI_API_KEY")
google_key = os.getenv("GOOGLE_API_KEY")

if gemini_key:
    print(f"✅ GEMINI_API_KEY: Found (length: {len(gemini_key)})")
elif google_key:
    print(f"✅ GOOGLE_API_KEY: Found (length: {len(google_key)})")
else:
    print("❌ GEMINI_API_KEY/GOOGLE_API_KEY: Not found")

# Check other important env vars
mongo_uri = os.getenv("MONGO_URI")
if mongo_uri:
    print(f"✅ MONGO_URI: Found")
else:
    print("❌ MONGO_URI: Not found")

secret_key = os.getenv("SECRET_KEY")
if secret_key:
    print(f"✅ SECRET_KEY: Found")
else:
    print("❌ SECRET_KEY: Not found")

print("\n🔧 Recommendations:")
if not openai_key:
    print("- Set OPENAI_API_KEY in your .env file")
if not gemini_key and not google_key:
    print("- Set GEMINI_API_KEY or GOOGLE_API_KEY in your .env file")
if not mongo_uri:
    print("- Set MONGO_URI in your .env file")
if not secret_key:
    print("- Set SECRET_KEY in your .env file")

print("\n📝 Example .env file:")
print("""
OPENAI_API_KEY=sk-your-openai-key-here
GEMINI_API_KEY=your-gemini-key-here
MONGO_URI=mongodb://localhost:27017
SECRET_KEY=your-secret-key-here
""")
