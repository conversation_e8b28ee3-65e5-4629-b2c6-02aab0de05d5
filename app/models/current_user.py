from typing import Any, Optional
from pydantic import BaseModel
from app.models.user import User
from pymongo import MongoClient, AsyncMongoClient

from app.core.logger import StructuredLogger
logger = StructuredLogger(__name__)

class CurrentUserDB(BaseModel):
    """Organized database access for current user"""
    client: MongoClient  # MongoDB client
    async_client: AsyncMongoClient  # Async MongoDB client
    read_db: Any  # Read database
    write_db: Optional[Any] = None  # Write database (admin only)
    
    class Config:
        arbitrary_types_allowed = True


class CurrentUserQdrant(BaseModel):
    """Organized Qdrant access for current user
    
    All fields are optional as they will be initialized lazily when needed.
    """
    qdrant: Optional[Any] = None  # Async Qdrant client
    qdrant_sync: Optional[Any] = None  # Sync Qdrant client
    qd_index: Optional[Any] = None  # Async VectorStoreIndex
    qd_index_sync: Optional[Any] = None  # Sync VectorStoreIndex
    act_index: Optional[Any] = None  # Async VectorStoreIndex for activities
    act_index_sync: Optional[Any] = None  # Sync VectorStoreIndex for activities
    summary_index: Optional[Any] = None  # Async VectorStoreIndex for summaries
    async_summary_index: Optional[Any] = None  # Sync VectorStoreIndex for summaries
    constitution_split_index: Optional[Any] = None  # Constitution index
    
    class Config:
        arbitrary_types_allowed = True


class CurrentUserLLM(BaseModel):
    """Organized LLM access for current user
    
    All fields are optional as they will be initialized lazily when needed.
    """
    openai: Optional[Any] = None  # OpenAI LLM client
    gemini: Optional[Any] = None  # Gemini LLM client
    
    class Config:
        arbitrary_types_allowed = True


class CurrentUser(BaseModel):
    """
    Organized current user structure with clean access patterns:
    - currentuser.user: User information
    - currentuser.db: Database access (client, read_db, write_db)
    - currentuser.qdrant: All Qdrant indexes and clients (lazy loaded)
    - currentuser.llm: LLM clients and managers (lazy loaded)
    """
    user: User  # User information
    tenant_id: str
    tenant_name: str
    db: CurrentUserDB  # Database access
    _qdrant_initializer: Any = None  # Internal: Qdrant client initializer
    _llm_initializer: Any = None  # Internal: LLM client initializer
    _qdrant_initialized: bool = False
    _llm_initialized: bool = False
    _qdrant_instance: Optional[CurrentUserQdrant] = None
    _llm_instance: Optional[CurrentUserLLM] = None
    
    class Config:
        arbitrary_types_allowed = True
        underscore_attrs_are_private = True
    
    @property
    async def qdrant(self) -> CurrentUserQdrant:
        """Lazy load Qdrant clients and indices when first accessed"""
        if not self._qdrant_initialized and self._qdrant_initializer:
            try:
                # Initialize Qdrant clients
                (qdrant_async, qdrant_sync, 
                 qd_index_async, qd_index_sync, 
                 act_index, act_sync_index, 
                 summary_index, async_summary_index, 
                 constitution_index) = await self._qdrant_initializer()
                
                self._qdrant_instance = CurrentUserQdrant(
                    qdrant=qdrant_async,
                    qdrant_sync=qdrant_sync,
                    qd_index=qd_index_async,
                    qd_index_sync=qd_index_sync,
                    act_index=act_index,
                    act_index_sync=act_sync_index,
                    summary_index=summary_index,
                    async_summary_index=async_summary_index,
                    constitution_split_index=constitution_index
                )
                self._qdrant_initialized = True
            except Exception as e:
                from app.core.logger import StructuredLogger
                logger = StructuredLogger(__name__)
                logger.error(f"Failed to initialize Qdrant clients: {e}")
                raise
        return self._qdrant_instance or CurrentUserQdrant()
    
    @property
    async def llm(self) -> CurrentUserLLM:
        """Lazy load LLM clients when first accessed"""
        if not self._llm_initialized and self._llm_initializer:
            try:
                logger.debug("🔄 Initializing LLM clients...")

                llm_clients = await self._llm_initializer()
                logger.debug(f"🔍 LLM clients created: openai={llm_clients.openai is not None}, gemini={llm_clients.gemini is not None}")

                self._llm_instance = CurrentUserLLM(**llm_clients.dict())
                self._llm_initialized = True
                logger.debug("✅ LLM clients initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize LLM clients: {e}")
                raise
        return self._llm_instance or CurrentUserLLM()
    
    async def ensure_initialized(self, services: list[str] = None):
        """Ensure specified services are initialized
        
        Args:
            services: List of services to initialize ('qdrant', 'llm')
                     If None, initializes all services
        """
        if services is None:
            services = ['qdrant', 'llm']
            
        if 'qdrant' in services and not self._qdrant_initialized:
            await self.qdrant  # This will trigger initialization
            
        if 'llm' in services and not self._llm_initialized:
            await self.llm  # This will trigger initialization
        
    def has_write_access(self) -> bool:
        """Check if user has write database access"""
        return self.db.write_db is not None
        
    def get_db_for_operation(self, operation_type: str = "read") -> Any:
        """Get appropriate database based on operation type"""
        if operation_type.lower() == "write":
            if self.db.write_db is None:
                from fastapi import HTTPException
                raise HTTPException(
                    status_code=403,
                    detail="Write access denied. Admin role required."
                )
            return self.db.write_db
        return self.db.read_db
