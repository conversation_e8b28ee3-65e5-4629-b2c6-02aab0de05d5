"""
Production-ready middleware for the Legal Backend API
Includes rate limiting, request logging, and performance monitoring
"""

import time
import asyncio
from collections import defaultdict, deque
from typing import Dict, Deque
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.logger import StructuredLogger

logger = StructuredLogger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware with sliding window algorithm
    
    Features:
    - Per-IP rate limiting
    - Different limits for different endpoints
    - Sliding window algorithm for accurate rate limiting
    - Automatic cleanup of old entries
    """
    
    def __init__(self, app, calls_per_minute: int = 60, burst_limit: int = 10):
        super().__init__(app)
        self.calls_per_minute = calls_per_minute
        self.burst_limit = burst_limit
        self.requests: Dict[str, Deque[float]] = defaultdict(deque)
        self.last_cleanup = time.time()
        
        # Different limits for different endpoint types
        self.endpoint_limits = {
            "/chat/": {"calls_per_minute": 20, "burst_limit": 5},  # AI endpoints are expensive
            "/v1/chat/": {"calls_per_minute": 20, "burst_limit": 5},
            "/health": {"calls_per_minute": 300, "burst_limit": 50},  # Health checks can be frequent
            "/metrics": {"calls_per_minute": 120, "burst_limit": 20},
            "/docs": {"calls_per_minute": 30, "burst_limit": 10},
        }
    
    def get_client_ip(self, request: Request) -> str:
        """Extract client IP with proxy support"""
        # Check for forwarded headers (common in production behind load balancers)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
            
        return request.client.host if request.client else "unknown"
    
    def get_rate_limit_for_path(self, path: str) -> Dict[str, int]:
        """Get rate limit configuration for specific path"""
        for endpoint_prefix, limits in self.endpoint_limits.items():
            if path.startswith(endpoint_prefix):
                return limits
        
        # Default limits
        return {"calls_per_minute": self.calls_per_minute, "burst_limit": self.burst_limit}
    
    def cleanup_old_requests(self):
        """Remove old request timestamps to prevent memory leaks"""
        current_time = time.time()
        
        # Only cleanup every 5 minutes to avoid performance impact
        if current_time - self.last_cleanup < 300:
            return
            
        cutoff_time = current_time - 60  # Remove requests older than 1 minute
        
        for ip in list(self.requests.keys()):
            # Remove old timestamps
            while self.requests[ip] and self.requests[ip][0] < cutoff_time:
                self.requests[ip].popleft()
            
            # Remove empty entries
            if not self.requests[ip]:
                del self.requests[ip]
        
        self.last_cleanup = current_time
    
    async def dispatch(self, request: Request, call_next):
        # Skip rate limiting for health checks in development
        if request.url.path in ["/health", "/metrics"] and request.headers.get("User-Agent", "").startswith("curl"):
            return await call_next(request)
        
        client_ip = self.get_client_ip(request)
        current_time = time.time()
        path = request.url.path
        
        # Get rate limits for this endpoint
        limits = self.get_rate_limit_for_path(path)
        calls_per_minute = limits["calls_per_minute"]
        burst_limit = limits["burst_limit"]
        
        # Cleanup old requests periodically
        self.cleanup_old_requests()
        
        # Get request history for this IP
        ip_requests = self.requests[client_ip]
        
        # Remove requests older than 1 minute
        minute_ago = current_time - 60
        while ip_requests and ip_requests[0] < minute_ago:
            ip_requests.popleft()
        
        # Check burst limit (requests in last 10 seconds)
        ten_seconds_ago = current_time - 10
        recent_requests = sum(1 for req_time in ip_requests if req_time > ten_seconds_ago)
        
        if recent_requests >= burst_limit:
            logger.warning(f"Rate limit exceeded (burst): {client_ip} - {recent_requests} requests in 10s")
            raise HTTPException(
                status_code=429,
                detail=f"Rate limit exceeded. Maximum {burst_limit} requests per 10 seconds.",
                headers={"Retry-After": "10"}
            )
        
        # Check per-minute limit
        if len(ip_requests) >= calls_per_minute:
            logger.warning(f"Rate limit exceeded (per-minute): {client_ip} - {len(ip_requests)} requests in 1m")
            raise HTTPException(
                status_code=429,
                detail=f"Rate limit exceeded. Maximum {calls_per_minute} requests per minute.",
                headers={"Retry-After": "60"}
            )
        
        # Record this request
        ip_requests.append(current_time)
        
        # Add rate limit headers to response
        response = await call_next(request)
        response.headers["X-RateLimit-Limit"] = str(calls_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(max(0, calls_per_minute - len(ip_requests)))
        response.headers["X-RateLimit-Reset"] = str(int(current_time + 60))
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Request logging middleware for monitoring and debugging
    """
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        client_ip = request.headers.get("X-Forwarded-For", request.client.host if request.client else "unknown")
        
        # Log request
        logger.info(f"Request: {request.method} {request.url.path} from {client_ip}")
        
        try:
            response = await call_next(request)
            
            # Calculate response time
            process_time = time.time() - start_time
            
            # Log response
            logger.info(f"Response: {response.status_code} in {process_time:.3f}s")
            
            # Add request ID for tracing
            response.headers["X-Request-ID"] = f"{int(start_time * 1000)}-{hash(client_ip) % 10000}"
            
            return response
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(f"Request failed: {request.method} {request.url.path} - {str(e)} in {process_time:.3f}s")
            raise


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """
    Performance monitoring middleware to track slow requests
    """
    
    def __init__(self, app, slow_request_threshold: float = 2.0):
        super().__init__(app)
        self.slow_request_threshold = slow_request_threshold
    
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        
        # Log slow requests
        if process_time > self.slow_request_threshold:
            logger.warning(f"Slow request: {request.method} {request.url.path} took {process_time:.3f}s")
        
        # Add performance headers
        response.headers["X-Response-Time"] = f"{process_time:.3f}"
        
        return response
