# src/core/security.py

import asyncio
from datetime import datetime, timedelta
from argon2 import Password<PERSON>asher
from argon2.exceptions import VerifyMismatchError
# from passlib.context import CryptContext
from fastapi.security import OAuth2<PERSON>assword<PERSON>earer
from fastapi import Depends, HTTPException
# Initialize Qdrant and LLM clients
from app.core.qdrant_client import create_qdrant_clients
from app.core.llm_client import create_llm_clients
import jwt
from app.core.config import SECRET_KEY, ALGORITHM
from app.models.user import User
from app.models.current_user import CurrentUser, CurrentUserDB, CurrentUserQdrant, CurrentUserLLM
from app.models.permission import Permission
from app.models.role import Role
from app.core.database import get_user_databases_async, get_db_from_tenant_id
from typing import Optional, List, Callable, Any
from types import CoroutineType
from app.core.logger import StructuredLogger

logger = StructuredLogger(__name__)

ph = PasswordHasher()
# pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="login",
    scheme_name="Bearer Token",
    description="Use credentials: username=admin, password=password, client_id=client_id",
    auto_error=False,  # Don't automatically return 401, let us handle it
)

def create_access_token(data: dict, expires_delta:timedelta = timedelta(days=1)) -> str:
    to_encode = data.copy() 
    if expires_delta:   
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def get_tenant_info(
    init_qdrant: bool = False,
    init_llm: bool = False,
    token: str = Depends(oauth2_scheme)
) -> CurrentUser:
    """Get user tenant information with optional service initialization
    
    Args:
        init_qdrant: Whether to initialize Qdrant clients
        init_llm: Whether to initialize LLM clients
        token: JWT token from Authorization header
        
    Returns:
        CurrentUser: User object with requested services initialized
    """
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    async def _get_user():
        try:
            payload = jwt.decode(jwt=token, key=SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            tenant_id: str = payload.get("tenant_id")

            if not username or not tenant_id:
                raise credentials_exception

            # Get user databases with role-based access
            user_dbs = await get_user_databases_async(tenant_id=tenant_id)
            read_db = user_dbs["read_db"]

            # Find user in read database
            user_doc = await read_db.users.find_one({"username": username})
            if not user_doc:
                raise credentials_exception

            # Get user permissions
            user_permissions = []
            for permission_name in user_doc.get("permissions", []):
                perm_doc = await read_db.permissions.find_one({"name": permission_name})
                if perm_doc:
                    user_permissions.append(Permission(**perm_doc))

            # Update user document with permissions
            user_doc["permissions"] = user_permissions
            
            # Create User object
            user = User(**user_doc)
            
            # Get databases with proper role-based access
            user_dbs_with_roles = await get_user_databases_async(tenant_id=tenant_id, user_role=user.role)
            
            # Create lazy initializers for services
            async def get_qdrant_clients():
                qdrant_result = await create_qdrant_clients(user_dbs_with_roles["read_db"])
                if any(client is None for client in qdrant_result[:4]):
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to initialize Qdrant vector database connection"
                    )
                return qdrant_result
                
            async def get_llm_clients():
                return await create_llm_clients(user_dbs_with_roles["read_db"])

            # Create the user with lazy initializers
            current_user = CurrentUser(
                user=user,
                tenant_id=tenant_id,
                tenant_name=user_dbs_with_roles["tenant_name"],
                db=CurrentUserDB(
                    client=user_dbs_with_roles["client"],
                    async_client=user_dbs_with_roles["async_client"],
                    read_db=user_dbs_with_roles["read_db"],
                    write_db=user_dbs_with_roles["write_db"]
                ),
                _qdrant_initializer=get_qdrant_clients if init_qdrant else None,
                _llm_initializer=get_llm_clients if init_llm else None
            )
            
            # Initialize services if requested
            if init_qdrant or init_llm:
                await current_user.ensure_initialized(
                    services=(["qdrant"] if init_qdrant else []) + 
                            (["llm"] if init_llm else [])
                )
                
            return current_user
            
        except jwt.PyJWTError:
            raise credentials_exception
        except Exception as e:
            logger.error(f"Error in get_tenant_info: {str(e)}")
            if not isinstance(e, HTTPException):
                raise HTTPException(status_code=500, detail="Internal server error")
            raise

    # Return the coroutine directly (FastAPI will await it)
    return _get_user()


# Clean dependency functions
def require(roles: List[str] = None, db: bool = True, llm: bool = False, qdrant: bool = False):
    """
    Universal dependency function for authentication and service initialization

    Args:
        roles: List of required roles (optional, if None then any authenticated user)
        db: Whether to initialize database access (default: True)
        llm: Whether to initialize LLM clients (default: False)
        qdrant: Whether to initialize Qdrant clients (default: False)

    Usage:
        # Database only (fastest, default)
        current_user: CurrentUser = Depends(require())
        current_user: CurrentUser = Depends(require(db=True))

        # With roles
        current_user: CurrentUser = Depends(require(roles=["admin", "lawyer"]))

        # With LLM only
        current_user: CurrentUser = Depends(require(llm=True))

        # With Qdrant only
        current_user: CurrentUser = Depends(require(qdrant=True))

        # With both LLM and Qdrant
        current_user: CurrentUser = Depends(require(llm=True, qdrant=True))

        # With roles and services
        current_user: CurrentUser = Depends(require(roles=["admin"], llm=True, qdrant=True))

        # Explicit combinations for clarity
        current_user: CurrentUser = Depends(require(db=True, llm=True))
        current_user: CurrentUser = Depends(require(db=True, qdrant=True))
        current_user: CurrentUser = Depends(require(db=True, llm=True, qdrant=True))
    """
    async def dependency(token: str = Depends(oauth2_scheme)) -> CurrentUser:
        # Get user with requested services (db is always True by default)
        current_user = await get_tenant_info(init_llm=llm, init_qdrant=qdrant, token=token)

        # Check roles if specified
        if roles and not current_user.user.has_any_role(roles):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {', '.join(roles)}"
            )

        return current_user

    return dependency

def require_roles(required_roles: List[str], init_llm: bool = False, init_qdrant: bool = False):
    """
    Dependency to check if user has any of the required roles with optimized service initialization

    Args:
        required_roles: List of roles that are allowed to access the endpoint
        init_llm: Whether to initialize LLM clients (default: False)
        init_qdrant: Whether to initialize Qdrant clients (default: False)

    Usage:
        # Database only (fastest)
        current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))

        # With LLM
        current_user: CurrentUser = Depends(require_roles(["admin"], init_llm=True))

        # With both LLM and Qdrant
        current_user: CurrentUser = Depends(require_roles(["admin"], init_llm=True, init_qdrant=True))
    """
    async def check_roles(current_user: CurrentUser = Depends(
        lambda token=Depends(oauth2_scheme): get_tenant_info(init_llm=init_llm, init_qdrant=init_qdrant, token=token)
    )):
        if not current_user.user.has_any_role(required_roles):
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {', '.join(required_roles)}"
            )
        return current_user
    return check_roles

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, invited_by, role
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


# Replace CryptContext with PasswordHasher
def hash_password(password: str) -> str:
    """
    Hash new passwords using Argon2
    """
    return ph.hash(password)
    
# Update the verify_password function
def verify_password(plain_password, hashed_password) -> bool:
    """
    Verifies a plain password against a hashed password.
    """
    try:
        return ph.verify(hash=hashed_password, password=plain_password)
    except VerifyMismatchError:
        return False


def require_permissions(required_permissions: list[str]) :
    """
    Dependency that checks if the user has all the required permissions.
    Usage: @router.get("/endpoint", dependencies=[Depends(require_permissions(["read:users", "write:users"]))])
    """
    async def check_permissions(current_user: CurrentUser = Depends(dependency=get_tenant_info)) -> CurrentUser:
        user_permissions = [p.name for p in current_user.user.permissions]
        
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return current_user
    return check_permissions


