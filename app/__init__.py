from fastapi import <PERSON><PERSON><PERSON>, responses, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.sessions import SessionMiddleware
import time
import os
from app.v1.api import v1_api  # must be a FastAPI instance, not just an APIRouter
from app.core.middleware import RateLimitMiddleware, RequestLoggingMiddleware, PerformanceMonitoringMiddleware
app = FastAPI(
    title="Legal Backend API",
    description="Production-ready FastAPI backend for legal document processing with AI-powered chat, multi-tenant architecture, and enterprise-grade security.",
    version="1.0.0",
    openapi_version="3.1.0",
    servers=[
        {"url": "/", "description": "Main Server - System Management"},
        {"url": "/v1", "description": "API v1 - Legal Services & Chat"}
    ],
    docs_url="/docs",
    redoc_url="/redoc"
)

# Production-ready middleware stack
# 1. Security headers and compression
app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if os.getenv("ENVIRONMENT") == "DEVELOPMENT" else os.getenv("ALLOWED_HOSTS", "")
)

# 2. Session middleware for security
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
)

# 3. CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:5174",
        "https://yourdomain.com"  # Add your production domain
    ] if os.getenv("ENVIRONMENT") != "development" else ["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["X-Request-ID", "X-Response-Time"]
)

# 4. Custom middleware for request tracking and security headers
@app.middleware("http")
async def add_security_headers_and_timing(request: Request, call_next):
    start_time = time.time()

    response = await call_next(request)

    # Add security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

    # Add timing header
    process_time = time.time() - start_time
    response.headers["X-Response-Time"] = str(process_time)

    return response
# 5. Production middleware for monitoring and rate limiting
if os.getenv("ENVIRONMENT") != "development":
    # Rate limiting for production
    app.add_middleware(RateLimitMiddleware, calls_per_minute=100, burst_limit=20)

# Performance monitoring (always enabled)
app.add_middleware(PerformanceMonitoringMiddleware, slow_request_threshold=2.0)

# Request logging (always enabled)
app.add_middleware(RequestLoggingMiddleware)

# Mount versioned sub-apps
app.mount("/v1", v1_api)

@app.get("/", include_in_schema=False)
async def root():
    """Root endpoint - redirects to API documentation with server selection"""
    return responses.RedirectResponse(url="/docs")

@app.get("/hello", tags=["Main"])
async def hello_main():
    """Simple health check endpoint"""
    return {"message": "Hello from Legal Backend API", "version": "1.0.0"}

@app.get("/health", tags=["Main"])
async def health_check():
    """Minimal health check endpoint for monitoring"""
    try:
        # Basic health indicators
        health_data = {
            "status": "healthy",
            "service": "legal-backend",
            "version": "1.0.0",
            "timestamp": time.time(),
            "uptime": time.time() - start_time if start_time else 0
        }

        # Test database connectivity (basic check)
        try:
            from app.core.database import _get_mongo_uri
            from pymongo import MongoClient
            client = MongoClient(_get_mongo_uri(), serverSelectionTimeoutMS=3000)
            client.admin.command('ping')
            health_data["database"] = "connected"
            client.close()
        except Exception as e:
            health_data["database"] = "error"
            health_data["status"] = "degraded"

        return health_data

    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "legal-backend",
            "timestamp": time.time()
        }



# Use modern lifespan events instead of deprecated on_event
from contextlib import asynccontextmanager

# Global variable to track startup time
start_time = None

@asynccontextmanager
async def lifespan(_: FastAPI):
    global start_time
    # Startup
    start_time = time.time()
    print("🚀 Legal Backend API starting up...")
    print(f"📊 Environment: {os.getenv('ENVIRONMENT', 'development')}")
    print(f"🔒 Security headers enabled")
    print(f"⚡ Compression enabled")

    # Performance optimizations
    try:
        from app.core.performance import warm_up_connections
        await warm_up_connections()
        print("🔥 Database connections warmed up")
    except Exception as e:
        print(f"⚠️  Warning: Failed to warm up connections: {e}")

    yield

    # Shutdown
    print("👋 Legal Backend API shutting down...")
    try:
        from app.core.performance import cleanup_connections
        await cleanup_connections()
        print("🧹 Connections cleaned up")
    except Exception as e:
        print(f"⚠️  Warning: Failed to cleanup connections: {e}")

# Apply lifespan to app
app.router.lifespan_context = lifespan


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)