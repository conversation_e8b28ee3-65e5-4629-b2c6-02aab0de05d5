from fastapi import Fast<PERSON><PERSON>, responses
from app.v1.api import v1_api  # must be a FastAPI instance, not just an APIRouter
from fastapi.middleware.cors import CORSMiddleware
app = FastAPI(
    title="Legal Backend API",
    description="Production-ready FastAPI backend for legal document processing with AI-powered chat, multi-tenant architecture, and enterprise-grade security.",
    version="1.0.0",
    openapi_version="3.1.0",
    servers=[
        {"url": "/", "description": "Main Server - System Management"},
        {"url": "/v1", "description": "API v1 - Legal Services & Chat"}
    ],
    docs_url="/docs",
    redoc_url="/redoc"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*","http://localhost:3000","http://localhost:5173","http://localhost:5174"],  # Adjust this to your needs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# Mount versioned sub-apps
app.mount("/v1", v1_api)

@app.get("/", include_in_schema=False)
async def root():
    """Root endpoint - redirects to API documentation with server selection"""
    return responses.RedirectResponse(url="/docs")

@app.get("/hello", tags=["Main"])
async def hello_main():
    """Simple health check endpoint"""
    return {"message": "Hello from Legal Backend API", "version": "1.0.0"}

@app.get("/health", tags=["Main"])
async def health_check():
    """Health check endpoint for monitoring"""
    return {"status": "healthy", "service": "legal-backend"}

# Use modern lifespan events instead of deprecated on_event
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(_: FastAPI):
    # Startup
    print("🚀 Legal Backend API starting up...")
    yield
    # Shutdown
    print("👋 Legal Backend API shutting down...")

# Apply lifespan to app
app.router.lifespan_context = lifespan


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)