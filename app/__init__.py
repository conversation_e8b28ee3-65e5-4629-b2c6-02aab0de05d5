from fastapi import <PERSON><PERSON><PERSON>, responses, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from starlette.middleware.sessions import SessionMiddleware
import time
import os
from app.v1.api import v1_api  # must be a FastAPI instance, not just an APIRouter
app = FastAPI(
    title="Legal Backend API",
    description="Production-ready FastAPI backend for legal document processing with AI-powered chat, multi-tenant architecture, and enterprise-grade security.",
    version="1.0.0",
    openapi_version="3.1.0",
    servers=[
        {"url": "/", "description": "Main Server - System Management"},
        {"url": "/v1", "description": "API v1 - Legal Services & Chat"}
    ],
    docs_url="/docs",
    redoc_url="/redoc"
)

# Production-ready middleware stack
# 1. Security headers and compression
app.add_middleware(GZipMiddleware, minimum_size=1000)
import json

# Parse ALLOWED_HOSTS from JSON string to Python list
def get_allowed_hosts():
    if os.getenv("ENVIRONMENT") == "DEVELOPMENT":
        return ["*"]
    
    allowed_hosts = os.getenv("ALLOWED_HOSTS", "[]")
    try:
        hosts = json.loads(allowed_hosts)
        # Add Docker internal hosts
        docker_hosts = [
            "localhost",
            "0.0.0.0",
            "127.0.0.1",
            "host.docker.internal",
            "legal-backend"  # Docker service name
        ]
        # Merge and deduplicate
        return list(set(hosts + docker_hosts))
    except json.JSONDecodeError:
        return ["*"]

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=get_allowed_hosts()
)

# 2. Session middleware for security
# app.add_middleware(
#     SessionMiddleware,
#     secret_key=os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
# )

# 3. CORS middleware
# CORS configuration
cors_origins = ["*"] if os.getenv("ENVIRONMENT") == "DEVELOPMENT" else [
    "http://localhost:5173",
    "http://localhost:5174",
    "http://localhost:3000",
    "http://127.0.0.1:5173",
    "http://127.0.0.1:5174",
    "http://127.0.0.1:3000",
    "http://localhost:8020",
    "http://127.0.0.1:8020"
] + get_allowed_hosts()

app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=600
)

# 4. Custom middleware for request tracking and security headers
@app.middleware("http")
async def add_security_headers_and_timing(request: Request, call_next):
    start_time = time.time()

    response = await call_next(request)

    # Add security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"

    # Add timing header
    process_time = time.time() - start_time
    response.headers["X-Response-Time"] = str(process_time)

    return response

# Mount versioned sub-apps
app.mount("/v1", v1_api)

@app.get("/", include_in_schema=False)
async def root():
    """Root endpoint - redirects to API documentation with server selection"""
    return responses.RedirectResponse(url="/docs")

@app.get("/hello", tags=["Main"])
async def hello_main():
    """Simple health check endpoint"""
    return {"message": "Hello from Legal Backend API", "version": "1.0.0"}

@app.get("/health", tags=["Main"])
async def health_check():
    """Minimal health check endpoint for monitoring"""
    try:
        # Basic health indicators
        health_data = {
            "status": "healthy",
            "service": "legal-backend",
            "version": "1.0.0",
            "timestamp": time.time(),
            "uptime": time.time() - start_time if start_time else 0
        }

        # Test database connectivity (basic check)
        try:
            from app.core.database import _get_mongo_uri
            from pymongo import MongoClient
            client = MongoClient(_get_mongo_uri(), serverSelectionTimeoutMS=3000)
            client.admin.command('ping')
            health_data["database"] = "connected"
            client.close()
        except Exception as e:
            health_data["database"] = "error"
            health_data["status"] = "degraded"

        return health_data

    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "legal-backend",
            "timestamp": time.time()
        }



# Use modern lifespan events instead of deprecated on_event
from contextlib import asynccontextmanager

# Global variable to track startup time
start_time = None

@asynccontextmanager
async def lifespan(_: FastAPI):
    global start_time
    # Startup
    start_time = time.time()
    print("🚀 Legal Backend API starting up...")
    print(f"📊 Environment: {os.getenv('ENVIRONMENT', 'development')}")
    print(f"🔒 Security headers enabled")
    print(f"⚡ Compression enabled")

    # Performance optimizations
    try:
        from app.core.performance import warm_up_connections
        await warm_up_connections()
        print("🔥 Database connections warmed up")
    except Exception as e:
        print(f"⚠️  Warning: Failed to warm up connections: {e}")

    yield

    # Shutdown
    print("👋 Legal Backend API shutting down...")
    try:
        from app.core.performance import cleanup_connections
        await cleanup_connections()
        print("🧹 Connections cleaned up")
    except Exception as e:
        print(f"⚠️  Warning: Failed to cleanup connections: {e}")

# Apply lifespan to app
app.router.lifespan_context = lifespan


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)