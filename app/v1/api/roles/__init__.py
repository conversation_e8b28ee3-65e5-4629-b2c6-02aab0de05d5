from fastapi import APIRouter, Depends, HTTPException
from typing import List

from app.core.security import require_roles
from app.models.current_user import CurrentUser
from app.models.role import RoleCreate, RoleUpdate

router = APIRouter(tags=["Roles"])

@router.post("/roles/")
async def create_role(
    role: RoleCreate,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))
):
    """Create a new role"""
    roles_collection = current_user.db.read_db.roles
    permissions_collection = current_user.db.read_db.permissions
    
    # Check if role already exists
    if await roles_collection.find_one({"name": role.name}, {"_id": 0}):
        raise HTTPException(status_code=400, detail="Role already exists")
    
    # Validate that all permissions exist
    for permission in role.default_permissions:
        if not await permissions_collection.find_one({"name": permission}, {"_id": 0}):
            raise HTTPException(
                status_code=400, 
                detail=f"Permission '{permission}' does not exist"
            )
    
    role_data = {
        "name": role.name,
        "default_permissions": role.default_permissions
    }
    
    try:
        result = await roles_collection.insert_one(role_data)
        created_role = await roles_collection.find_one({"_id": result.inserted_id}, {"_id": 0})
        return created_role
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/roles/")
async def read_roles(
    skip: int = 0,
    limit: int = 100,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer", "user"]))
):
    """Get all roles"""
    roles_collection = current_user.db.read_db.roles
    roles = await roles_collection.find({}, {"_id": 0}).skip(skip).limit(limit).to_list(None)
    return roles

@router.patch("/roles/{role_name}/permissions")
async def update_role_permissions(
    role_name: str,
    permissions: RoleUpdate,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))
):
    """Update role's default permissions"""
    roles_collection = current_user.db.read_db.roles
    permissions_collection = current_user.db.read_db.permissions
    
    # Find the role
    role = await roles_collection.find_one({"name": role_name}, {"_id": 0})
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")
    
    # Validate that all permissions exist
    for permission in permissions.default_permissions:
        if not await permissions_collection.find_one({"name": permission}, {"_id": 0}):
            raise HTTPException(
                status_code=400, 
                detail=f"Permission '{permission}' does not exist"
            )
    
    try:
        result = await roles_collection.update_one(
            {"name": role_name},
            {"$set": {"default_permissions": permissions.default_permissions}}
        )
        
        if result.modified_count == 0:
            raise HTTPException(status_code=400, detail="No changes made to role")
            
        updated_role = await roles_collection.find_one({"name": role_name}, {"_id": 0})
        return updated_role
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/roles/{role_name}")
async def delete_role(
    role_name: str,
    current_user: CurrentUser = Depends(require_roles(["admin", "lawyer"]))
):
    """Delete a role"""
    roles_collection = current_user.db.read_db.roles
    
    # Find the role
    role = await roles_collection.find_one({"name": role_name}, {"_id": 0})
    if not role:
        raise HTTPException(status_code=404, detail="Role not found")
    try:
        result = await roles_collection.delete_one({"name": role_name})
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=400, detail="Failed to delete role")
            
        return {"message": f"Role '{role_name}' successfully deleted"}
        
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
