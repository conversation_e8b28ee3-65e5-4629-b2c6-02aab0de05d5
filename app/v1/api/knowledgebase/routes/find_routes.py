from fastapi import APIRouter, Depends, HTTPException
from app.core.security import require
from app.models.current_user import CurrentUser
from app.core.utils.response import PaginatedResponse
from app.v1.api.knowledgebase.models import FindRequest
from app.v1.api.knowledgebase.core import find_documents_data

router = APIRouter()


@router.post("/find", response_model=PaginatedResponse)
async def find_knowledgebase(
    find_request: FindRequest,
    current_user: CurrentUser = Depends(require())  # Database only - no LLM/Qdrant needed
):
    """Find knowledgebase using POST with filters from GET /filter response"""
    try:
        collection = current_user.read_db.documents
        return await find_documents_data(collection, find_request)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error: {str(e)}")
