from fastapi import APIRouter, Depends, HTTPException
from app.core.security import get_tenant_info, oauth2_scheme
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from typing import List, Dict, Any
import time
from llama_index.core import VectorStoreIndex
from collections import defaultdict
from bson import ObjectId


router = APIRouter()
logger = StructuredLogger(__name__)


@router.get("/retrieve", response_model=Dict[str, Any])
async def retrieve_documents(
    query: str,
    top_k: int = 15,
    current_user: CurrentUser = Depends(lambda token=Depends(oauth2_scheme): get_tenant_info(init_qdrant=True, token=token))  # Qdrant only
):
    """Retrieve documents using dense embeddings (optimized for Nepali-English cross-lingual search)"""
    start_time = time.time()

    try:
        logger.log(f"Retrieval request: '{query}' from user {current_user.user.id}")
        logger.test(f"Parameters: top_k={top_k}")

        # Check if Qdrant index is available
        if not current_user.qd_index:
            logger.exception("Qdrant index not available")
            raise HTTPException(
                status_code=503,
                detail="Qdrant index not available"
            )

        logger.debug("Creating dense retriever for cross-lingual search")

        # Create retriever from the index (optimized for Nepali-English)
        retriever = current_user.qd_index.as_retriever(
            similarity_top_k=top_k,
            similarity_cutoff=0.1  # Lower threshold for cross-lingual search
        )

        # Retrieve relevant documents
        logger.test(f"Executing retrieval for: '{query}'")
        retrieval_start = time.time()
        retrieved_nodes = await retriever.aretrieve(query)
        retrieval_duration = time.time() - retrieval_start

        logger.log_retrieval("dense", query, len(retrieved_nodes), retrieval_duration)

        if not retrieved_nodes:
            logger.issue(f"No documents found for query: '{query}'")
            return {"response": f"No documents found for: '{query}'", "sources": []}

        logger.debug(f"Processing {len(retrieved_nodes)} retrieved nodes")

        # Group nodes by article_mongo_id
        articles_dict = defaultdict(list)
        print(f"Grouping {len(retrieved_nodes)} nodes by article_mongo_id")
        for node in retrieved_nodes:
            metadata = node.metadata if hasattr(node, 'metadata') else {}
            article_id = metadata.get('article_mongo_id', 'unknown')
            
            chunk_data = {
                "chunk_id": node.node_id,
                "score": round(node.score, 4) if hasattr(node, 'score') else 0.0,
                "text": node.text,
                "metadata": metadata
            }

            articles_dict[article_id].append(chunk_data)

        # Sort chunks within each article by score (highest first)
        for article_id in articles_dict:
            articles_dict[article_id].sort(key=lambda x: x['score'], reverse=True)

        logger.debug(f"Grouped into {len(articles_dict)} articles")

        # Convert to list format and fetch complete article data
        sources = []
        for article_id, chunks in articles_dict.items():
            # Skip unknown articles
            if article_id == 'unknown':
                continue
    
            # Fetch complete article from documents collection
            try:
                article_doc = await current_user.read_db.documents.find_one({"_id": ObjectId(article_id)})
                if not article_doc:
                    logger.issue(f"Article with ID {article_id} not found in database")
                    continue

                # Convert ObjectId to string for JSON serialization
                article_metadata = {}
                for key, value in article_doc.items():
                    if key == '_id':
                        article_metadata['_id'] = str(value)
                    else:
                        article_metadata[key] = value

                article_data = {
                    "article_mongo_id": article_id,
                    "metadata": article_metadata,
                    "chunks": chunks
                }

                sources.append(article_data)

            except Exception as e:
                logger.exception(f"Error fetching article {article_id}: {str(e)}")
                continue

        # Sort articles by best chunk score
        sources.sort(key=lambda x: max(chunk['score'] for chunk in x['chunks']), reverse=True)

        # Calculate final stats
        total_duration = time.time() - start_time
        total_chunks = sum(len(chunks) for chunks in articles_dict.values())

        logger.log(f"Retrieval completed: {len(sources)} articles, {total_chunks} chunks in {total_duration:.3f}s")

        response = f"Retrieved {total_chunks} relevant chunks from {len(sources)} legal documents"

        return {
            "response": response,
            "sources": sources,
            "stats": {
                "query": query,
                "total_articles": len(sources),
                "total_chunks": total_chunks,
                "retrieval_method": "dense_multilingual",
                "duration": round(total_duration, 3)
            }
        }

    except Exception as e:
        logger.exception(f"Retrieval failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Retrieval failed: {str(e)}"
        )

