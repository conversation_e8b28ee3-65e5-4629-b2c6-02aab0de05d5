"""
Ultra-Fast Legal RAG System
Parallel execution of CitationQueryEngine and MultiHopQueryEngine
Optimized for fact-based legal retrieval with maximum accuracy
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from app.core.security import get_tenant_info, oauth2_scheme
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from typing import Dict, List, Any
from collections import defaultdict
import asyncio
import time
import os
from datetime import datetime
from llama_index.core.query_engine import CitationQueryEngine
from llama_index.core.retrievers import VectorIndexRetriever
from bson import ObjectId
from llama_index.embeddings.jinaai import JinaEmbedding

router = APIRouter()
logger = StructuredLogger(__name__)


async def citation_query_engine(query: str, current_user: CurrentUser) -> Dict[str, Any]:
    """
    High-accuracy citation-based query processing with enhanced retrieval
    Preserves original citation format from CitationQueryEngine
    """
    try:
        citation_engine = CitationQueryEngine.from_args(
            index=current_user.qdrant.qd_index,
            similarity_top_k=8,
            citation_chunk_size=512,
            llm=current_user.llm.openai,
        )
        
        result = await citation_engine.aquery(query)
        
        # Extract citation-formatted response with original citation markers
        citation_response = result.response if hasattr(result, 'response') else ""
        
        # Process sources with citation preservation and mapping
        processed = await _process_sources(result, current_user)
        processed["response"] = citation_response  # Keep original citation format
        
        # Ensure citation numbers are preserved in response
        if hasattr(result, 'source_nodes') and result.source_nodes:
            # Verify citation mapping is consistent
            citation_mapping = processed.get('citation_mapping', {})
            
            # Add verification that citations in response match sources
            processed["citation_verification"] = {
                "response_citations": len([c for c in citation_response.split('[') if c.startswith(tuple(f'{i+1}]' for i in range(len(result.source_nodes))))]),
                "source_count": len(result.source_nodes),
                "citation_mapping_size": len(citation_mapping)
            }
        
        return processed
        
    except Exception as e:
        logger.error(f"CitationQueryEngine failed: {e}")
        return {"sources": [], "response": "", "error": str(e), "citation_mapping": {}}


async def multi_hop_query_engine(query: str, current_user: CurrentUser) -> Dict[str, Any]:
    """
    Optimized multi-hop query engine using concept extraction and parallel search
    Based on core legal_rag multi-hop implementation for maximum accuracy
    Preserves citation numbering across all hops
    """
    try:
        # Initialize multi-hop search components
        base_engine = CitationQueryEngine.from_args(
            index=current_user.qdrant.qd_index,
            similarity_top_k=8,  # Balanced for speed and coverage
            citation_chunk_size=512,
            llm=current_user.llm.openai
        )
        
        # Perform multi-hop search with concept extraction
        all_sources = []
        reasoning_steps = []
        
        # Hop 1: Initial search
        initial_result = await base_engine.aquery(query)
        initial_nodes = initial_result.source_nodes
        all_sources.extend(initial_nodes)
        reasoning_steps.append(f"Initial search: {len(initial_nodes)} sources")
        
        if len(initial_nodes) == 0:
            processed = await _process_sources(initial_result, current_user)
            processed["reasoning_steps"] = reasoning_steps
            return processed
        
        # Hop 2: Concept-based expansion (if initial results are sparse)
        if len(initial_nodes) < 5:
            # Extract key concepts from top results
            combined_text = " ".join([node.text[:150] for node in initial_nodes[:2]])
            
            # Simple concept extraction without LLM for speed
            concepts = _extract_legal_concepts(combined_text)
            
            for concept in concepts[:2]:  # Limit to 2 concepts for speed
                concept_query = f"{query} {concept}"
                concept_result = await base_engine.aquery(concept_query)
                concept_nodes = concept_result.source_nodes
                
                # Add unique nodes only, preserving citation order
                existing_node_ids = {s.node_id for s in all_sources if hasattr(s, 'node_id')}
                new_nodes = [n for n in concept_nodes if n.node_id not in existing_node_ids]
                all_sources.extend(new_nodes[:3])  # Limit to 3 new sources per concept
                
                if new_nodes:
                    reasoning_steps.append(f"Concept '{concept}': +{len(new_nodes)} sources")
        
        # Create a mock result with all sources for processing
        class MockResult:
            def __init__(self, sources, response):
                self.source_nodes = sources
                self.response = response
        
        # Use initial response which has proper citation numbering
        response_text = initial_result.response if hasattr(initial_result, 'response') else ""
        mock_result = MockResult(all_sources, response_text)
        
        processed = await _process_sources(mock_result, current_user)
        processed["reasoning_steps"] = reasoning_steps
        
        return processed
        
    except Exception as e:
        logger.error(f"Multi-hop query failed: {e}")
        return {"sources": [], "response": "", "error": str(e), "citation_mapping": {}}


async def acts_search(query: str, current_user: CurrentUser) -> Dict[str, Any]:
    """
    Specialized citation engine for Acts and Regulations
    Uses a dedicated index for legal acts and regulations with Jina Embeddings (2048d)
    No post-processing needed since act_index is already pre-processed
    """
    try:
        # Check if act_index is available
        if not hasattr(current_user, 'act_index') or current_user.act_index is None:
            logger.warning("No act_index available for acts_search")
            return {"sources": [], "response": "", "citation_mapping": {}}
        
        # Initialize Jina Embedding model with 2048 dimensions
        embed_model = JinaEmbedding(
            api_key=os.getenv("JINA_API_KEY"),
            model="jina-embeddings-v4",
            embed_batch_size=50,
            task="text-matching",
            dimensions=2048
        )
            
        # Create citation engine with act_index and Jina Embedding
        citation_engine = CitationQueryEngine.from_args(
            index=current_user.act_index,
            similarity_top_k=5,  # Fewer results for acts to keep it focused
            citation_chunk_size=512,
            llm=current_user.llm.openai,
            embed_model=embed_model  # Use Jina Embedding for queries
        )
        
        # Execute query
        result = await citation_engine.aquery(query)
        
        # Simple processing for act_index since it doesn't have split_articles structure
        source_nodes = result.source_nodes
        response_text = result.response
        
        # Create citation mapping and sources directly from nodes
        citation_mapping = {}
        sources = []
        
        for i, node in enumerate(source_nodes):
            citation_number = i + 1
            chunk_citation_id = f"[{citation_number}]"
            
            metadata = node.metadata if hasattr(node, 'metadata') else {}
            
            # Map citation number to source info
            citation_mapping[citation_number] = {
                "node_id": node.node_id if hasattr(node, 'node_id') else str(node.hash),
                "text": node.text,
                "metadata": metadata
            }
            
            # Create source entry for act
            source_data = {
                "node_id": node.node_id if hasattr(node, 'node_id') else str(node.hash),
                "citation_number": citation_number,
                "citation_id": chunk_citation_id,
                "score": round(float(node.score or 0), 4) if hasattr(node, 'score') else 0.0,
                "text": node.text,
                "metadata": metadata,
                "source_type": "act"
            }
            
            sources.append(source_data)
        
        return {
            "response": response_text,
            "sources": sources,
            "citation_mapping": citation_mapping,
            "source_type": "act"
        }
        
    except Exception as e:
        logger.error(f"Acts search failed: {e}")
        return {"sources": [], "response": "", "error": str(e), "citation_mapping": {}, "source_type": "act"}


def _extract_legal_concepts(text: str) -> List[str]:
    """
    Extract legal concepts from text for multi-hop expansion
    Fast rule-based extraction without LLM calls
    """
    if not text:
        return []
    
    # Legal keywords and phrases for Nepali legal context
    legal_keywords = [
        "land", "property", "ownership", "rights", "court", "judge", "law", 
        "act", "regulation", "case", "decision", "contract", "agreement",
        "dispute", "claim", "evidence", "witness", "trial", "judgment",
        "compensation", "damages", "liability", "negligence", "tort",
        "constitution", "amendment", "article", "section", "clause",
        "jurisdiction", "procedure", "appeal", "petition", "motion"
    ]
    
    # Nepali legal terms
    nepali_legal_terms = [
        "जग्गा", "सम्पत्ति", "अधिकार", "अदालत", "न्यायाधीश", "कानून",
        "मुद्दा", "फैसला", "सम्झौता", "विवाद", "दावी", "प्रमाण",
        "क्षतिपूर्ति", "दायित्व", "संविधान", "धारा", "ऐन", "नियम"
    ]
    
    text_lower = text.lower()
    concepts = []
    
    # Extract matching concepts
    for keyword in legal_keywords + nepali_legal_terms:
        if keyword.lower() in text_lower and keyword not in concepts:
            concepts.append(keyword)
    
    # Limit to top 3 most relevant concepts
    return concepts[:3]


async def _process_sources(result, current_user: CurrentUser) -> Dict[str, Any]:
    """
    Common function to process and group sources like /retrieve route
    Preserves citation numbering from CitationQueryEngine
    """
    source_nodes = result.source_nodes
    response_text = result.response
    
    # Create mapping between citation numbers and source nodes
    citation_mapping = {}
    
    # Group nodes by article_mongo_id like in /retrieve route
    articles_dict = defaultdict(list)
    
    for i, node in enumerate(source_nodes):
        metadata = node.metadata if hasattr(node, 'metadata') else {}
        article_id = metadata.get('article_mongo_id', 'unknown')
        chunk_id = metadata.get('chunk_id', 'unknown')
        
        # Get actual chunk text from split_articles collection
        chunk_doc = await current_user.db.read_db.split_articles.find_one(
            {"metadata.article_mongo_id": article_id, "metadata.chunk_id": chunk_id},
            {"text": 1, "_id": 0}
        )
        chunk_text = chunk_doc.get('text', node.text) if chunk_doc else node.text
        
        # Use citation number from CitationQueryEngine (1-based indexing)
        citation_number = i + 1
        chunk_citation_id = f"[{citation_number}]"
        
        # Map citation number to source info
        citation_mapping[citation_number] = {
            "article_id": article_id,
            "chunk_id": chunk_id,
            "text": chunk_text,
            "metadata": metadata
        }
        
        chunk_data = {
            "chunk_id": node.node_id if hasattr(node, 'node_id') else str(node.hash),
            "citation_id": chunk_citation_id,
            "citation_number": citation_number,
            "score": round(float(node.score or 0), 4) if hasattr(node, 'score') else 0.0,
            "text": chunk_text,
            "metadata": metadata or {}
        }
        
        articles_dict[article_id].append(chunk_data)
    
    # Sort chunks within each article by score (highest first)
    for article_id in articles_dict:
        articles_dict[article_id].sort(key=lambda x: x['score'], reverse=True)
    
    # Convert to list format and fetch complete article data
    sources = []
    for article_id, chunks in articles_dict.items():
        # Skip unknown articles
        if article_id == 'unknown':
            continue
        
        # Fetch complete article from documents collection
        try:
            article_doc = await current_user.db.read_db.documents.find_one({"_id": ObjectId(article_id)})
            if not article_doc:
                logger.error(f"Article with ID {article_id} not found in database")
                continue
            
                # Convert ObjectId and other non-serializable fields to strings
            article_metadata = {}
            for key, value in article_doc.items():
                if key == '_id':
                    article_metadata['_id'] = str(value)
                elif isinstance(value, (ObjectId, datetime)):
                    article_metadata[key] = str(value)
                elif isinstance(value, dict):
                    # Recursively process nested dictionaries
                    article_metadata[key] = {k: str(v) if isinstance(v, (ObjectId, datetime)) else v 
                                           for k, v in value.items()}
                else:
                    article_metadata[key] = value
            
            # Collect all citation numbers from chunks in this article
            citation_numbers = [chunk['citation_number'] for chunk in chunks]
            citation_ids = [chunk['citation_id'] for chunk in chunks]
            
            article_data = {
                "article_mongo_id": article_id,
                "citation_numbers": citation_numbers,
                "citation_ids": citation_ids,
                "metadata": article_metadata,
                "chunks": chunks
            }
            
            sources.append(article_data)
            
        except Exception as e:
            logger.error(f"Error fetching article {article_id}: {str(e)}")
            continue
    
    # Sort articles by best chunk score
    sources.sort(key=lambda x: max(chunk['score'] for chunk in x['chunks']), reverse=True)
    
    return {
        "response": response_text,
        "sources": sources,
        "citation_mapping": citation_mapping
    }


def _merge_sources(citation_sources: List[Dict], multi_hop_sources: List[Dict], act_sources: List[Dict] = None) -> Dict[str, List[Dict]]:
    """
    Intelligently merge sources from multiple engines
    Preserves citation information and creates consistent numbering across engines
    
    Args:
        citation_sources: Results from citation query engine
        multi_hop_sources: Results from multi-hop query engine
        act_sources: Results from acts search engine (optional)
        
    Returns:
        Dictionary containing merged sources by type
    """
    if act_sources is None:
        act_sources = []
    
    def process_sources(sources: List[Dict], source_type: str) -> List[Dict]:
        """Process and deduplicate sources for a specific type"""
        merged_dict = {}
        
        for source in sources:
            article_id = source.get("article_mongo_id")
            if not article_id:
                continue
                
            if article_id not in merged_dict:
                merged_dict[article_id] = {
                    "article_mongo_id": article_id,
                    "metadata": source.get("metadata", {}),
                    "chunks": [],
                    "source_type": source_type
                }
            
            # Add unique chunks
            existing_chunk_ids = {c["chunk_id"] for c in merged_dict[article_id]["chunks"]}
            for chunk in source.get("chunks", []):
                if chunk["chunk_id"] not in existing_chunk_ids:
                    chunk_data = dict(chunk)
                    chunk_data["source_engine"] = source_type
                    merged_dict[article_id]["chunks"].append(chunk_data)
        
        # Sort chunks by score
        for article in merged_dict.values():
            article["chunks"].sort(key=lambda x: x["score"], reverse=True)
            
        return list(merged_dict.values())
    
    # Process each source type
    citation_processed = process_sources(citation_sources, "legal")
    multi_hop_processed = process_sources(multi_hop_sources, "legal")
    
    # Handle act sources differently since they don't have article structure
    act_processed = []
    if act_sources:
        for i, act_source in enumerate(act_sources):
            act_data = {
                "node_id": act_source.get("node_id", f"act_{i}"),
                "citation_number": act_source.get("citation_number", i + 1),
                "citation_id": act_source.get("citation_id", f"[{i + 1}]"),
                "score": act_source.get("score", 0.0),
                "text": act_source.get("text", ""),
                "metadata": act_source.get("metadata", {}),
                "source_type": "act"
            }
            act_processed.append(act_data)
    
    # Merge citation and multi-hop sources for legal results
    legal_merged = {}
    for source in citation_processed + multi_hop_processed:
        article_id = source["article_mongo_id"]
        if article_id not in legal_merged:
            legal_merged[article_id] = source
        else:
            # Merge chunks, preferring citation sources
            existing_chunk_ids = {c["chunk_id"] for c in legal_merged[article_id]["chunks"]}
            for chunk in source["chunks"]:
                if chunk["chunk_id"] not in existing_chunk_ids:
                    legal_merged[article_id]["chunks"].append(chunk)
    
    # Sort legal results by best chunk score
    legal_results = list(legal_merged.values())
    legal_results.sort(key=lambda x: max(c["score"] for c in x["chunks"]), reverse=True)
    
    # Sort act results by score
    act_results = sorted(act_processed, key=lambda x: x["score"], reverse=True)
    
    # Assign citation numbers
    def assign_citations(sources: List[Dict]) -> List[Dict]:
        citation_counter = 1
        for source in sources:
            if "chunks" in source:  # Legal sources
                article_citation_numbers = []
                article_citation_ids = []
                
                for chunk in source["chunks"]:
                    chunk["citation_number"] = citation_counter
                    chunk["citation_id"] = f"[{citation_counter}]"
                    
                    article_citation_numbers.append(citation_counter)
                    article_citation_ids.append(f"[{citation_counter}]")
                    citation_counter += 1
                
                source["citation_numbers"] = article_citation_numbers
                source["citation_ids"] = article_citation_ids
            else:  # Act sources
                source["citation_number"] = citation_counter
                source["citation_id"] = f"[{citation_counter}]"
                citation_counter += 1
        
        return sources
    
    # Assign citations to both legal and act sources
    legal_results = assign_citations(legal_results)
    act_results = assign_citations(act_results)
    
    return {
        "legal": legal_results,
        "acts": act_results
    }


# Ultra-fast API endpoint
@router.get("/query")
async def legal_rag_query(
    query: str,
    current_user: CurrentUser = Depends(lambda token=Depends(oauth2_scheme): get_tenant_info(init_llm=True, init_qdrant=True, token=token))  # Both LLM and Qdrant needed
):
    """
    Ultra-fast Legal RAG - direct parallel execution
    Returns document-based answers without LLM synthesis
    """
    try:
        if not query.strip():
            raise HTTPException(status_code=400, detail="Query required")
        
        logger.log(f"Ultra-fast Legal RAG: '{query}' from user {current_user.user.id}")
        
        # Execute parallel citation and multi-hop queries for maximum accuracy
        start_time = time.time()
        
        # Run all engines in parallel for comprehensive coverage
        tasks = [
            citation_query_engine(query, current_user),
            multi_hop_query_engine(query, current_user)
        ]
        
        # Only include acts_search if act_index is available
        if hasattr(current_user, 'act_index') and current_user.qdrant.act_index is not None:
            tasks.append(acts_search(query, current_user))
        
        # Execute all available tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Unpack results
        citation_result = results[0]
        multi_hop_result = results[1]
        acts_result = results[2] if len(results) > 2 else {"sources": [], "citation_mapping": {}}
        
        # Process results and handle any exceptions
        if isinstance(citation_result, Exception):
            logger.error(f"Citation query failed: {str(citation_result)}")
            citation_result = {"sources": [], "citation_mapping": {}}
            
        if isinstance(multi_hop_result, Exception):
            logger.error(f"Multi-hop query failed: {str(multi_hop_result)}")
            multi_hop_result = {"sources": [], "citation_mapping": {}}
            
        if isinstance(acts_result, Exception):
            logger.error(f"Acts search failed: {str(acts_result)}")
            acts_result = {"sources": [], "citation_mapping": {}}
            
        # Ensure all results have the expected structure
        citation_result = citation_result or {"sources": [], "citation_mapping": {}}
        multi_hop_result = multi_hop_result or {"sources": [], "citation_mapping": {}}
        acts_result = acts_result or {"sources": [], "citation_mapping": {}}
        
        # Merge sources from all engines
        merged_results = _merge_sources(
            citation_result.get("sources", []), 
            multi_hop_result.get("sources", []), 
            acts_result.get("sources", [])
        )
        
        # Get response from citation engine if available, otherwise from multi-hop
        response_text = ""
        if citation_result and not isinstance(citation_result, Exception) and citation_result.get("response"):
            response_text = citation_result["response"]
        elif multi_hop_result and not isinstance(multi_hop_result, Exception) and multi_hop_result.get("response"):
            response_text = multi_hop_result["response"]
        
        # Combine citation mappings
        combined_citation_mapping = {}
        if citation_result and not isinstance(citation_result, Exception):
            combined_citation_mapping.update(citation_result.get("citation_mapping", {}))
        if acts_result and not isinstance(acts_result, Exception):
            combined_citation_mapping.update(acts_result.get("citation_mapping", {}))
        
        # Build response with proper citation preservation using actual chunk citations
        response_parts = []
        
        # Use the combined_citation_mapping we already created
        citation_mapping = combined_citation_mapping
        
        if merged_results and (merged_results.get('legal') or merged_results.get('acts')):
            response_parts.append("Based on comprehensive legal document analysis:")
            
            # Create comprehensive citation mapping from actual chunks and acts
            all_sources = []
            
            # Process legal sources
            for source in merged_results.get("legal", []):
                for chunk in source.get('chunks', []):
                    citation_num = chunk.get('citation_number')
                    if citation_num:
                        citation_mapping[str(citation_num)] = {
                            "citation_id": chunk.get('citation_id', f"[{citation_num}]"),
                            "article_mongo_id": source.get('article_mongo_id'),
                            "chunk_id": chunk.get('chunk_id'),
                            "text": await current_user.db.read_db.najir_split_documents.find_one({"metadata.sentence_id": chunk.get("metadata", {}).get("sentence_id")},{"text": 1}),
                            "score": chunk.get('score', 0.0),
                            "metadata": chunk.get('metadata', {}),
                            "source_type": "legal"
                        }
                        all_sources.append({
                            'chunk': chunk,
                            'source': source,
                            'citation_number': citation_num,
                            'source_type': 'legal'
                        })
            
            # Process act sources
            for act_source in merged_results.get("acts", []):
                citation_num = act_source.get('citation_number')
                if citation_num:
                    citation_mapping[str(citation_num)] = {
                        "citation_id": act_source.get('citation_id', f"[{citation_num}]"),
                        "node_id": act_source.get('node_id'),
                        "text": act_source.get('text', ''),
                        "score": act_source.get('score', 0.0),
                        "metadata": act_source.get('metadata', {}),
                        "source_type": "act"
                    }
                    all_sources.append({
                        'act_source': act_source,
                        'citation_number': citation_num,
                        'source_type': 'act'
                    })
            
            # Group legal sources by article and show act sources separately
            article_chunks = {}
            act_citations = []
            
            for source_info in all_sources:
                if source_info['source_type'] == 'legal':
                    source = source_info['source']
                    chunk = source_info['chunk']
                    citation_num = source_info['citation_number']
                    article_id = source.get('article_mongo_id', 'unknown')
                    
                    if article_id not in article_chunks:
                        article_chunks[article_id] = []
                    article_chunks[article_id].append({
                        'citation_number': citation_num,
                        'chunk': chunk,
                        'source': source
                    })
                else:  # act source
                    act_citations.append(source_info)
            
            # Build response using actual citation numbers from chunks
            for article_id, chunks_info in article_chunks.items():
                if chunks_info:
                    # Sort chunks by citation number for consistent ordering
                    chunks_info.sort(key=lambda x: x['citation_number'])
                    
                    citation_nums = [str(info['citation_number']) for info in chunks_info]
                    article_title = chunks_info[0]['source'].get('metadata', {}).get('title', 'Legal Document')
                    response_parts.append(f"\n**{article_title}** [{', '.join(citation_nums)}]")
                    
                    # Add key excerpts from chunks with their actual citation numbers
                    for info in chunks_info[:3]:  # Limit to 3 chunks per article
                        chunk = info['chunk']
                        citation_num = info['citation_number']
                        excerpt = chunk.get('text', '')# Limit excerpt length
                        response_parts.append(f"  • {excerpt}... [{citation_num}]")
            
            # Add act citations
            if act_citations:
                response_parts.append("\n**Legal Acts and Regulations:**")
                for act_info in act_citations[:5]:  # Limit to 5 act citations
                    act_source = act_info['act_source']
                    citation_num = act_info['citation_number']
                    excerpt = act_source.get('text', '')[:200]
                    response_parts.append(f"  • {excerpt}... [{citation_num}]")
        
        combined_response = '\n'.join(response_parts) if response_parts else "No relevant legal documents found."
        
        total_duration = time.time() - start_time
        
        # Simple serialization that just handles ObjectId and leaves act sources as is
        def clean_dict(d):
            if isinstance(d, dict):
                result = {}
                for k, v in d.items():
                    if isinstance(v, ObjectId):
                        result[k] = str(v)
                    elif isinstance(v, dict):
                        result[k] = clean_dict(v)
                    elif isinstance(v, list):
                        result[k] = [clean_dict(i) if isinstance(i, dict) else 
                                   str(i) if isinstance(i, ObjectId) else i 
                                   for i in v]
                    else:
                        result[k] = v
                return result
            elif isinstance(d, ObjectId):
                return str(d)
            elif isinstance(d, list):
                return [clean_dict(i) for i in d]
            return d
            
        all_sources_for_response = []
        
        # Process legal sources with basic cleaning
        legal_sources = merged_results.get("legal", [])
        for source in legal_sources:
            all_sources_for_response.append(clean_dict(source))
            
        # Add act sources as-is without modification
        all_sources_for_response.extend(merged_results.get("acts", []))
        
        # Clean citation_mapping to handle ObjectId objects
        cleaned_citation_mapping = clean_dict(citation_mapping)
        
        logger.log(f"Parallel query completed: {len(all_sources_for_response)} sources in {total_duration:.3f}s")
        
        return {
            "response": combined_response,
            "sources": all_sources_for_response,
            "citation_mapping": cleaned_citation_mapping,
            "metadata": {
                "citation_sources": len(citation_result.get("sources", [])),
                "multi_hop_sources": len(multi_hop_result.get("sources", [])),
                "act_sources": len(acts_result.get("sources", [])),
                "total_sources": len(all_sources_for_response),
                "duration": total_duration,
                "citation_format": "numbered_brackets",
                "total_chunks": sum(len(source.get('chunks', [])) if 'chunks' in source else 1 for source in all_sources_for_response)
            }
        }
        
    except Exception as e:
        logger.error(f"Ultra-fast Legal RAG failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))