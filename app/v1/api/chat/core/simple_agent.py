from app.core.security import require
from app.models.current_user import CurrentUser
from app.core.logger import StructuredLogger
from app.v1.api.chat.core.tools import LegalTools
from app.v1.api.chat.core.utils.process_najirs import _process_sources
from app.v1.api.chat.core.utils.process_citation import process_citation


from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, BaseMessage, SystemMessage, AIMessage
from llama_index.llms.gemini import Gemini
from llama_index.core.base.llms.types import CompletionResponse

from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
from fastapi import APIRouter, Depends, Query
from typing import Annotated, List, Optional, Dict, Any
import json
import asyncio
import time
from datetime import datetime, timezone
import urllib.parse

logger = StructuredLogger(__name__)
router = APIRouter()

class OptimizedAgentState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    original_query: str
    is_legal_query: bool
    refined_query: str
    tools_to_run: List[str]
    tool_responses: Dict[str, Any]
    all_sources: List[dict]
    final_response: str
    citation_mapping: Dict[str, Any]
    step_times: Dict[str, float]

def get_current_timestamp():
    """Get current UTC timestamp in ISO format"""
    return datetime.now(timezone.utc).isoformat()

class OptimizedLegalAgent:
    def __init__(self, current_user: CurrentUser):
        logger.info("🚀 OPTIMIZED AGENT INIT - Starting with Gemini-first approach")

        self.current_user = current_user
        # self.llm: Gemini = current_user.llm.gemini
        self.llm_openai=current_user.llm.openai  # Single LLM for everything
        self.llm_gemini=current_user.llm.gemini
        self.tools = []
        self.legal_tools = None
        self.checkpointer = AsyncMongoDBSaver(
            client=current_user.db.async_client,
            db_name=current_user.db.read_db.name,
            collection_name="checkpoints"
        )
        self.graph = None
        
        logger.info("✅ OPTIMIZED AGENT INIT COMPLETE - Gemini ready")

    async def setup_tools_async(self):
        """Setup tools asynchronously"""
        if self.tools:
            return
            
        user_id = str(self.current_user.user.id)
        logger.info(f"🔨 ASYNC TOOLS SETUP - For user: {user_id}")
        
        try:
            self.legal_tools = LegalTools()
            self.tools = self.legal_tools.create_tools(self.current_user)
            logger.info(f"✅ ASYNC TOOLS READY - Created {len(self.tools)} tools")
        except Exception as e:
            logger.error(f"❌ ASYNC TOOLS SETUP FAILED: {e}")
            self.tools = []

    async def step1_smart_analysis(self, state: OptimizedAgentState):
        """STEP 1: Smart query analysis with Gemini"""
        step_start = time.time()
        logger.info("🧠 STEP 1 START - Smart Gemini analysis")

        user_query = state.get("original_query", state["messages"][-1].content)
        logger.info(f"📝 ANALYZING QUERY: {user_query}")

        # Ensure tools are ready
        await self.setup_tools_async()

        analysis_prompt = await self.current_user.db.read_db.prompts.find_one({"name": "analysis_prompt"}, {"prompt": 1})
        analysis_prompt = analysis_prompt.get("prompt", "").format(
            user_query=user_query
        )
        logger.info(f"📝 ANALYSIS PROMPT: {analysis_prompt}")
        try:
            # Use async Gemini completion
            analysis_response: CompletionResponse = await self.llm_gemini.acomplete(prompt=analysis_prompt)
            
            # Parse JSON response
            response_text = analysis_response.text.strip()
            if '```json' in response_text:
                response_text = response_text.split('```json')[1].split('```')[0].strip()
            elif '```' in response_text:
                response_text = response_text.split('```')[1].strip()
            
            analysis_data = json.loads(response_text)
            
            is_legal = analysis_data.get("is_legal", False)
            refined_query = analysis_data.get("refined_query", user_query)
            tools_needed = analysis_data.get("tools_needed", [])
            reasoning = analysis_data.get("reasoning", "")
            
            logger.info(f"🏛️ LEGAL QUERY: {is_legal}")
            logger.info(f"🔧 REFINED: {refined_query}")
            logger.info(f"🛠️ TOOLS: {tools_needed}")
            logger.info(f"💡 REASONING: {reasoning}")
            
        except Exception as e:
            logger.warning(f"Analysis parsing failed: {e}, defaulting to legal query")
            is_legal = True
            refined_query = user_query
            tools_needed = ["najir_search", "act_search", "najir_summary"]

        # Handle non-legal queries immediately
        if not is_legal:
            step_time = time.time() - step_start
            
            non_legal_response = f"""I specialize in Nepali legal research and analysis. Your query "{user_query}" doesn't appear to be legal-related.

I can help you with:
- Nepali case law and court precedents
- Legal statutes and regulations
- Legal procedures and rights
- Legal analysis and guidance

Please ask a legal question, and I'll provide comprehensive research with proper citations."""
            
            logger.info(f"⚠️ NON-LEGAL QUERY - Direct response in {step_time:.2f}s")
            
            return {
                "is_legal_query": False,
                "refined_query": refined_query,
                "tools_to_run": [],
                "final_response": non_legal_response,
                "citation_mapping": {},
                "all_sources": [],
                "step_times": {"step1_analysis": step_time}
            }

        step_time = time.time() - step_start
        logger.info(f"✅ STEP 1 COMPLETE - Analysis done in {step_time:.2f}s")

        return {
            "is_legal_query": is_legal,
            "refined_query": refined_query,
            "tools_to_run": tools_needed,
            "step_times": {"step1_analysis": step_time}
        }

    async def step2_parallel_research(self, state: OptimizedAgentState):
        """STEP 2: Execute all tools in parallel"""
        step_start = time.time()
        logger.info("⚡ STEP 2 START - Parallel research execution")

        # Skip if non-legal or no tools needed
        if not state.get("is_legal_query", True) or state.get("final_response"):
            logger.info("⏭️ SKIPPING TOOLS - Non-legal query")
            step_time = time.time() - step_start
            return {
                "tool_responses": {},
                "all_sources": [],
                "step_times": {**state.get("step_times", {}), "step2_research": step_time}
            }

        tools_to_run = state.get("tools_to_run", [])
        refined_query = state.get("refined_query", state.get("original_query", ""))
        
        logger.info(f"🔍 PARALLEL SEARCH: {len(tools_to_run)} tools with query: {refined_query}")

        # Create tool execution tasks
        tasks = []
        tool_names = []
        
        for tool_name in tools_to_run:
            tool_instance = None
            for tool in self.tools:
                if tool.name == tool_name:
                    tool_instance = tool
                    break
            
            if tool_instance:
                tasks.append(tool_instance.arun(refined_query))
                tool_names.append(tool_name)
                logger.info(f"📋 QUEUED: {tool_name}")
            else:
                logger.warning(f"❌ TOOL NOT FOUND: {tool_name}")

        # Execute all tools in parallel
        logger.info(f"🚀 EXECUTING {len(tasks)} tools simultaneously")
        
        results = []
        if tasks:
            try:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                logger.info(f"📊 RECEIVED {len(results)} results")
            except Exception as e:
                logger.error(f"❌ PARALLEL EXECUTION FAILED: {e}")

        # Process results
        tool_responses = {}
        all_sources = []
        citation_counter = 1

        for i, (result, tool_name) in enumerate(zip(results, tool_names)):
            if isinstance(result, Exception):
                logger.error(f"❌ {tool_name.upper()} ERROR: {result}")
                continue

            try:
                if isinstance(result, str):
                    parsed_result = json.loads(result)
                else:
                    parsed_result = result

                response_text = parsed_result.get('response', '')
                sources = parsed_result.get('sources', [])
                
                tool_responses[tool_name] = {
                    "response": response_text,
                    "sources": sources
                }
                
                # Process sources for citations
                for source in sources:
                    metadata = source.get("metadata", {})
                    source_data = {
                        "citation_number": citation_counter,
                        "tool": tool_name,
                        "text": source.get("text", ""),
                        "score": source.get("score", 0.0),
                        "metadata": metadata
                    }
                    all_sources.append(source_data)
                    citation_counter += 1
                
                logger.info(f"✅ {tool_name.upper()}: {len(sources)} sources, response: {len(response_text)} chars")
                
            except Exception as e:
                logger.error(f"❌ PROCESSING {tool_name.upper()} RESULT FAILED: {e}")

        step_time = time.time() - step_start
        logger.info(f"✅ STEP 2 COMPLETE - {len(all_sources)} total sources in {step_time:.2f}s")

        return {
            "tool_responses": tool_responses,
            "all_sources": all_sources,
            "step_times": {**state.get("step_times", {}), "step2_research": step_time}
        }

    async def step3_intelligent_synthesis(self, state: OptimizedAgentState):
        """STEP 3: Intelligent response synthesis with Gemini"""
        step_start = time.time()
        logger.info("🎯 STEP 3 START - Intelligent Gemini synthesis")

        # Return existing response if already set
        if state.get("final_response"):
            step_time = time.time() - step_start
            return {
                "final_response": state.get("final_response"),
                "citation_mapping": state.get("citation_mapping", {}),
                "step_times": {**state.get("step_times", {}), "step3_synthesis": step_time}
            }

        user_query = state.get("original_query", "")
        tool_responses = state.get("tool_responses", {})
        all_sources = state.get("all_sources", [])

        logger.info(f"📝 SYNTHESIZING: {len(tool_responses)} tool responses, {len(all_sources)} sources")

        # Build comprehensive context
        research_context = ""
        for tool_name, data in tool_responses.items():
            research_context += f"\n=== {tool_name.upper()} RESULTS ===\n"
            research_context += data.get("response", "")
            research_context += "\n"

        # Build source context with smart truncation
        sources_context = ""
        for source in all_sources[:20]:  # Limit to top 20 sources to avoid token limits
            sources_context += f"\n[{source['citation_number']}] {source['tool'].upper()}:\n"
            source_text = source['text'][:400] + "..." if len(source['text']) > 400 else source['text']
            sources_context += f"Text: {source_text}\n"
            
            metadata = source.get('metadata', {})
            if metadata.get('title'):
                sources_context += f"Title: {metadata['title']}\n"
            if metadata.get('court_type'):
                sources_context += f"Court: {metadata['court_type']}\n"
            if metadata.get('year'):
                sources_context += f"Year: {metadata['year']}\n"
            sources_context += "\n"

        # Synthesis prompt for Gemini
        synthesis_prompt_doc =await self.current_user.db.read_db.prompts.find_one({"name": "synthesis_prompt"}, {"prompt": 1})
        synthesis_prompt = synthesis_prompt_doc.get("prompt", "")
        synthesis_prompt = synthesis_prompt.format(
            user_query=user_query,
            research_context=research_context,
            sources_context=sources_context
        )
        logger.info(f"📝 SYNTHESIS PROMPT: {synthesis_prompt}")
        try:
            # Generate synthesis with Gemini
            synthesis_response: CompletionResponse = await self.llm_gemini.acomplete(prompt=synthesis_prompt)
            final_response = synthesis_response.text.format(
                research_context=research_context,
                sources_context=sources_context,
                user_query=user_query
            )
            
            # Build citation mapping
            citation_mapping = {}
            for source in all_sources:
                citation_num = str(source["citation_number"])
                citation_mapping[citation_num] = {
                    "tool": source["tool"],
                    "text": source["text"],
                    "score": source.get("score", 0.0),
                    "metadata": source.get("metadata", {})
                }
            
            step_time = time.time() - step_start
            logger.info(f"✅ STEP 3 COMPLETE - Synthesis ready in {step_time:.2f}s")
            
            return {
                "final_response": final_response,
                "citation_mapping": citation_mapping,
                "step_times": {**state.get("step_times", {}), "step3_synthesis": step_time}
            }
            
        except Exception as e:
            step_time = time.time() - step_start
            logger.error(f"❌ SYNTHESIS FAILED: {e}")
            
            fallback_response = f"I apologize, but I encountered an error while processing your legal query: {str(e)}"
            
            return {
                "final_response": fallback_response,
                "citation_mapping": {},
                "step_times": {**state.get("step_times", {}), "step3_synthesis": step_time}
            }

    def should_run_research(self, state: OptimizedAgentState) -> str:
        """Smart routing decision"""
        if state.get("final_response"):  # Non-legal query already handled
            return "END"
        elif state.get("is_legal_query", True) and state.get("tools_to_run"):
            return "step2_research"
        else:
            return "step3_synthesis"

    def build_graph(self):
        """Build optimized 3-step workflow"""
        logger.info("🏗️ BUILDING OPTIMIZED WORKFLOW")

        builder = StateGraph(OptimizedAgentState)

        # Add nodes
        builder.add_node("step1_analysis", self.step1_smart_analysis)
        builder.add_node("step2_research", self.step2_parallel_research)
        builder.add_node("step3_synthesis", self.step3_intelligent_synthesis)

        # Build flow
        builder.add_edge(START, "step1_analysis")
        
        builder.add_conditional_edges(
            "step1_analysis",
            self.should_run_research,
            {
                "step2_research": "step2_research",
                "step3_synthesis": "step3_synthesis",
                "END": "__end__"
            }
        )
        
        builder.add_edge("step2_research", "step3_synthesis")

        self.graph = builder.compile(checkpointer=self.checkpointer)
        logger.info("✅ OPTIMIZED WORKFLOW READY")
        return self.graph

    async def run(self, query: str, thread_id: Optional[str] = None):
        """Execute optimized workflow"""
        workflow_start = time.time()
        user_id = str(self.current_user.user.id)
        
        logger.info("🚀 OPTIMIZED WORKFLOW START")
        logger.info(f"👤 USER: {user_id}")
        logger.info(f"❓ QUERY: {query}")
        
        # Ensure everything is ready
        if not self.graph:
            self.build_graph()
        
        config = {
            "configurable": {"thread_id": thread_id or user_id}, 
            "recursion_limit": 50
        }
        
        initial_state = {
            "messages": [HumanMessage(content=query)],
            "original_query": query,
            "is_legal_query": True,
            "refined_query": "",
            "tools_to_run": [],
            "tool_responses": {},
            "all_sources": [],
            "final_response": "",
            "citation_mapping": {},
            "step_times": {}
        }
        
        # Execute workflow
        result = await self.graph.ainvoke(initial_state, config=config)
        
        workflow_time = time.time() - workflow_start
        logger.info(f"🏁 OPTIMIZED WORKFLOW COMPLETE - Total: {workflow_time:.2f}s")
        
        return result


@router.post("/agent")
async def optimized_legal_agent_chat(
    query: str = Query(..., description="The legal query to process"),
    current_user: CurrentUser = Depends(require(llm=True, qdrant=True))  # Both LLM and Qdrant needed
):
    """
    Optimized 3-step legal research agent with Gemini-first approach
    
    Features:
    - Smart query analysis
    - Parallel tool execution  
    - Intelligent synthesis
    - No hardcoded keywords
    - Full async/await
    """
    request_start = time.time()
    request_timestamp = get_current_timestamp()

    logger.info("📡 OPTIMIZED AGENT API START")
    logger.info(f"🕐 TIMESTAMP: {request_timestamp}")
    logger.info(f"👤 USER: {current_user.user.id}")
    logger.info(f"❓ QUERY: {query}")

    try:
        # Decode query
        decoded_query = urllib.parse.unquote(query)
        
        # Create and run optimized agent
        agent = OptimizedLegalAgent(current_user)
        result = await agent.run(decoded_query, str(current_user.user.id))

        # Process sources for response
        request_time = time.time() - request_start
        
        # Extract sources by tool type
        all_sources = result.get("all_sources", [])
        najir_sources = [s for s in all_sources if s.get("tool") == "najir_search"]
        act_sources = [s for s in all_sources if s.get("tool") == "act_search"]
        summary_sources = [s for s in all_sources if s.get("tool") == "najir_summary"]

        # Process najir sources
        processed_najir = await _process_sources(
            [{"text": s["text"], "metadata": s["metadata"]} for s in najir_sources], 
            current_user
        )
        citation_mapping = await process_citation(result.get("citation_mapping", {}), current_user)
        
        response_data = {
            "response": result.get("final_response", ""),
            "citation_mapping": citation_mapping,
            "najir_sources": processed_najir.get("sources", []),
            "act_sources": [{"text": s["text"], "metadata": s["metadata"]} for s in act_sources],
            "najir_summary_sources": [{"text": s["text"], "metadata": s["metadata"]} for s in summary_sources],
            "performance": {
                "total_time": request_time,
                "step_times": result.get("step_times", {}),
                "sources_count": len(all_sources)
            }
        }

        logger.info(f"✅ OPTIMIZED AGENT SUCCESS - {request_time:.2f}s, {len(all_sources)} sources")
        return response_data

    except Exception as e:
        request_time = time.time() - request_start
        logger.error(f"❌ OPTIMIZED AGENT ERROR - {request_time:.2f}s: {e}")

        return {
            "response": f"Error processing your request: {str(e)}",
            "citation_mapping": {},
            "najir_sources": [],
            "act_sources": [],
            "najir_summary_sources": [],
            "performance": {"total_time": request_time, "error": str(e)}
        }