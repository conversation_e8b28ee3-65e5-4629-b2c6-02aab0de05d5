[project]
name = "backend-template"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "argon2-cffi>=23.1.0",
    "bcrypt>=4.3.0",
    "crewai>=0.157.0",
    "fastapi[standard]>=0.115.11",
    "google-generativeai>=0.8.5",
    "hdbscan>=0.8.40",
    "httpx>=0.28.1",
    "itsdangerous>=2.2.0",
    "langchain>=0.3.27",
    "langchain-mongodb>=0.6.2",
    "langchain-openai>=0.3.29",
    "langextract>=1.0.5",
    "langgraph>=0.6.4",
    "langgraph-checkpoint-mongodb>=0.1.4",
    "llama-index>=0.12.52",
    "llama-index-agent-openai>=0.4.12",
    "llama-index-core>=0.12.52.post1",
    "llama-index-embeddings-jinaai>=0.4.0",
    "llama-index-embeddings-openai>=0.3.1",
    "llama-index-llms-gemini>=0.5.0",
    "llama-index-llms-openai>=0.4.7",
    "llama-index-vector-stores-qdrant>=0.6.1",
    "openai>=1.97.1",
    "pillow>=10.4.0",
    "psutil>=7.0.0",
    "pyjwt>=2.10.1",
    "pymongo>=4.11.2",
    "pymupdf>=1.26.3",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.3",
    "pytest-cov>=6.0.0",
    "pytest-mongodb>=2.4.0",
    "python-dotenv>=1.0.1",
    "python-multipart>=0.0.20",
    "qdrant-client>=1.15.0",
    "scikit-learn>=1.7.1",
    "stemmer>=0.0.4",
    "umap>=0.1.1",
    "uvicorn>=0.34.0",
]

[tool.pytest.ini_options]
pythonpath = "."
testpaths = ["app/v1/tests"]
python_files = "test_*.py"
addopts = "-v"
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[dependency-groups]
dev = [
    "ipykernel>=6.30.0",
]
